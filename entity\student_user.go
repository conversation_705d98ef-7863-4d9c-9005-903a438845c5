package entity

// 生成一个go的StudentUser实体类，使用playGround/validator做参数验证，要求：
// 1、ID字段,主键自增
// 2、UserName，5-20个字符，只允许：字母、数字、下划线
// 3、密码，不能为空
// 4、Email，不能为空，格式正确，默认值是""，3-254个字符
// 5、Phone，不能为空，格式正确，默认值是""，8-15 位数字
// 6、Status，不能为空，值为0表示禁用，1表示启用，默认值是1
// 7、Points，用户积分，不为空，默认值为0
// 8、CreateTime，自动采用当前时间，仅在创建的时候设置
// 9、UpdateTime，自动采用当前时间，每次更新的时候设置

// CREATE TABLE `student_user` (
//   `id` int unsigned NOT NULL AUTO_INCREMENT,
//   `user_name` varchar(20) NOT NULL,
//   `password` varchar(255) NOT NULL,
//   `email` varchar(254) NOT NULL DEFAULT '',
//   `phone` varchar(15) NOT NULL DEFAULT '',
//   `status` tinyint NOT NULL DEFAULT '1',
//   `points` int NOT NULL DEFAULT '0',
//   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
//   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
//   PRIMARY KEY (`id`),
// ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='学生用户表';

import (
	"time"
)

type StudentUser struct {
	ID         uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	UserName   string    `gorm:"size:20;not null" json:"user_name" validate:"required,min=5,max=20,alphanum_underscore"`
	Password   string    `gorm:"not null" json:"-" validate:"required"` // 密码不序列化到JSON
	Email      string    `gorm:"size:254;not null;default:''" json:"email" validate:"required,email,max=254,min=3"`
	Phone      string    `gorm:"size:15;not null;default:''" json:"phone" validate:"required,numeric,min=8,max=15"`
	Status     int8      `gorm:"not null;default:1" json:"status" validate:"required,oneof=0 1"`
	Points     int       `gorm:"not null;default:0" json:"points" validate:"required"`
	CreateTime time.Time `gorm:"autoCreateTime" json:"create_time"`
	UpdateTime time.Time `gorm:"autoUpdateTime" json:"update_time"`
}

// TableName 实现 Tabler 接口来指定自定义表名
func (StudentUser) TableName() string {
	return "student_user" // 返回你想要的表名
}
