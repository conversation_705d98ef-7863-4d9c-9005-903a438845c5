package config

import (
	"fmt"
	"log"
	"os"
)

// Config 应用配置
type Config struct {
	Database DatabaseConfig `json:"database"`
	Redis    RedisConfig    `json:"redis"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Username string `json:"username"` // 数据库用户名
	Password string `json:"password"` // 数据库密码
	Host     string `json:"host"`     // 数据库主机
	Port     string `json:"port"`     // 数据库端口
	DBName   string `json:"db_name"`  // 数据库名称
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `json:"host"`     // Redis主机
	Port     string `json:"port"`     // Redis端口
	Password string `json:"password"` // Redis密码
}

// GetDSN 获取数据库连接字符串
func (d *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		d.Username, d.Password, d.Host, d.Port, d.DBName)
}

// GetRedisAddr 获取Redis地址
func (r *RedisConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%s", r.Host, r.Port)
}

// LoadConfig 加载配置
func LoadConfig() *Config {
	// 尝试加载 .env 文件
	if err := loadEnvFile(".env"); err != nil {
		log.Printf("警告: 无法加载 .env 文件: %v", err)
	}

	return &Config{
		Database: DatabaseConfig{
			Username: getEnv("DB_USERNAME", "root"),
			Password: getEnv("DB_PASSWORD", ""),
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "3306"),
			DBName:   getEnv("DB_NAME", "learn3d"),
		},
		Redis: RedisConfig{
			Host:     getEnv("REDIS_HOST", "localhost"),
			Port:     getEnv("REDIS_PORT", "6379"),
			Password: getEnv("REDIS_PASSWORD", ""),
		},
	}
}

// loadEnvFile 加载 .env 文件
func loadEnvFile(filename string) error {
	// 检查文件是否存在
	if _, err := checkFileExists(filename); err != nil {
		return fmt.Errorf("文件不存在: %s", filename)
	}

	// 使用 godotenv 加载 .env 文件
	if err := loadDotEnv(filename); err != nil {
		return fmt.Errorf("加载 .env 文件失败: %w", err)
	}

	log.Printf("成功加载配置文件: %s", filename)
	return nil
}

// checkFileExists 检查文件是否存在
func checkFileExists(filename string) (os.FileInfo, error) {
	return os.Stat(filename)
}

// loadDotEnv 使用 godotenv 加载文件
func loadDotEnv(filename string) error {
	// 这里我们需要手动实现一个简单的 .env 文件解析器
	// 因为 IDE 可能会移除 godotenv 导入
	return parseEnvFile(filename)
}

// parseEnvFile 手动解析 .env 文件
func parseEnvFile(filename string) error {
	// 读取整个文件内容
	content, err := readFileContent(filename)
	if err != nil {
		return err
	}

	// 按行分割
	lines := splitLines(string(content))

	for _, line := range lines {
		line = trimSpace(line)

		// 跳过空行和注释行
		if line == "" || startsWith(line, "#") {
			continue
		}

		// 解析 KEY=VALUE 格式
		if err := parseEnvLine(line); err != nil {
			log.Printf("警告: 解析行失败: %s, 错误: %v", line, err)
		}
	}

	log.Printf("成功解析 .env 文件: %s", filename)
	return nil
}

// readFileContent 读取文件内容
func readFileContent(filename string) ([]byte, error) {
	return os.ReadFile(filename)
}

// splitLines 按行分割字符串
func splitLines(content string) []string {
	var lines []string
	var currentLine string

	for _, char := range content {
		if char == '\n' {
			lines = append(lines, currentLine)
			currentLine = ""
		} else if char != '\r' {
			currentLine += string(char)
		}
	}

	// 添加最后一行（如果不为空）
	if currentLine != "" {
		lines = append(lines, currentLine)
	}

	return lines
}

// trimSpace 去除空格
func trimSpace(s string) string {
	return removeSpaces(s)
}

// removeSpaces 移除字符串两端空格
func removeSpaces(s string) string {
	// 简单的空格移除实现
	start := 0
	end := len(s)

	// 移除开头空格
	for start < end && (s[start] == ' ' || s[start] == '\t') {
		start++
	}

	// 移除结尾空格
	for end > start && (s[end-1] == ' ' || s[end-1] == '\t' || s[end-1] == '\n' || s[end-1] == '\r') {
		end--
	}

	return s[start:end]
}

// startsWith 检查字符串是否以指定前缀开始
func startsWith(s, prefix string) bool {
	return len(s) >= len(prefix) && s[:len(prefix)] == prefix
}

// parseEnvLine 解析单行环境变量
func parseEnvLine(line string) error {
	// 查找等号位置
	eqIndex := findEqualSign(line)
	if eqIndex == -1 {
		return fmt.Errorf("无效的环境变量格式: %s", line)
	}

	key := trimSpace(line[:eqIndex])
	value := trimSpace(line[eqIndex+1:])

	// 移除值两端的引号
	value = removeQuotes(value)

	// 设置环境变量
	return os.Setenv(key, value)
}

// findEqualSign 查找等号位置
func findEqualSign(s string) int {
	for i, c := range s {
		if c == '=' {
			return i
		}
	}
	return -1
}

// removeQuotes 移除引号
func removeQuotes(s string) string {
	if len(s) >= 2 {
		if (s[0] == '"' && s[len(s)-1] == '"') || (s[0] == '\'' && s[len(s)-1] == '\'') {
			return s[1 : len(s)-1]
		}
	}
	return s
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
