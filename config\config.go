package config

import (
	"fmt"
	"os"
)

// Config 应用配置
type Config struct {
	Database DatabaseConfig `json:"database"`
	Redis    RedisConfig    `json:"redis"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Username string `json:"username"` // 数据库用户名
	Password string `json:"password"` // 数据库密码
	Host     string `json:"host"`     // 数据库主机
	Port     string `json:"port"`     // 数据库端口
	DBName   string `json:"db_name"`  // 数据库名称
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `json:"host"`     // Redis主机
	Port     string `json:"port"`     // Redis端口
	Password string `json:"password"` // Redis密码
}

// GetDSN 获取数据库连接字符串
func (d *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		d.Username, d.Password, d.Host, d.Port, d.DBName)
}

// GetRedisAddr 获取Redis地址
func (r *RedisConfig) GetRedisAddr() string {
	return fmt.Sprintf("%s:%s", r.Host, r.Port)
}

// LoadConfig 加载配置
func LoadConfig() *Config {
	return &Config{
		Database: DatabaseConfig{
			Username: getEnv("DB_USERNAME", "root"),
			Password: getEnv("DB_PASSWORD", ""),
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "3306"),
			DBName:   getEnv("DB_NAME", "learn3d"),
		},
		Redis: RedisConfig{
			Host:     getEnv("REDIS_HOST", "localhost"),
			Port:     getEnv("REDIS_PORT", "6379"),
			Password: getEnv("REDIS_PASSWORD", ""),
		},
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
