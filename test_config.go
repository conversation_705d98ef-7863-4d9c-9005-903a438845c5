package main

import (
	"fmt"
	"study_platform/config"
)

func main() {
	fmt.Println("=== 测试配置加载 ===")
	
	// 加载配置
	cfg := config.LoadConfig()

	// 打印数据库配置
	fmt.Println("\n=== 数据库配置 ===")
	fmt.Printf("用户名: %s\n", cfg.Database.Username)
	fmt.Printf("密码: %s\n", cfg.Database.Password)
	fmt.Printf("主机: %s\n", cfg.Database.Host)
	fmt.Printf("端口: %s\n", cfg.Database.Port)
	fmt.Printf("数据库名: %s\n", cfg.Database.DBName)
	fmt.Printf("连接字符串: %s\n", cfg.Database.GetDSN())

	// 打印Redis配置
	fmt.Println("\n=== Redis配置 ===")
	fmt.Printf("主机: %s\n", cfg.Redis.Host)
	fmt.Printf("端口: %s\n", cfg.Redis.Port)
	fmt.Printf("密码: %s\n", cfg.Redis.Password)
	fmt.Printf("Redis地址: %s\n", cfg.Redis.GetRedisAddr())
	
	fmt.Println("\n=== 测试完成 ===")
}
