package common

import (
	"time"
)

type Sort struct {
	SortBy     string `json:"sortBy"`
	Descending bool   `json:"descending"`
}

type Page struct {
	Page     int `json:"page"`
	PageSize int `json:"pageSize"`
}

func (page *Page) GetPage() int {
	if page.Page < 1 {
		return 1
	}
	return page.Page
}

func (page *Page) GetPageSize() int {
	if page.PageSize < 1 {
		return 10
	}
	return page.PageSize
}

func (page *Page) GetOffset() int {
	return (page.GetPage() - 1) * page.GetPageSize()
}

func (page *Page) GetLimit() int {
	return page.GetPageSize()
}

func NewPage(page int, pageSize int) *Page {
	return &Page{
		Page:     page,
		PageSize: pageSize,
	}
}

type PageResult[T any] struct {
	Page     int `json:"page"`
	PageSize int `json:"pageSize"`
	Total    int `json:"total"`
	Records  []T `json:"records"`
}

type PageSumResult[T any, ST any] struct {
	Page     int `json:"page"`
	PageSize int `json:"pageSize"`
	Total    int `json:"total"`
	Records  []T `json:"records"`
	SumInfo  ST  `json:"sumInfo"`
}

func (pageResult *PageResult[T]) GetPage() int {
	if pageResult.Page < 1 {
		return 1
	}
	return pageResult.Page
}

func (pageResult *PageResult[T]) GetPageSize() int {
	if pageResult.PageSize < 1 {
		return 10
	}
	return pageResult.PageSize
}

func (pageResult *PageResult[T]) GetTotal() int {
	return pageResult.Total
}

func (pageResult *PageResult[T]) GetRecords() []T {
	return pageResult.Records
}

type TimeRange struct {
	BeginTime *time.Time `json:"beginTime"`
	EndTime   *time.Time `json:"endTime"`
}

func (timeRange *TimeRange) GetBeginTime() *time.Time {
	if timeRange.BeginTime == nil {
		return nil
	}
	if timeRange.BeginTime.IsZero() {
		return nil
	}
	return timeRange.BeginTime
}

func (timeRange *TimeRange) GetEndTime() *time.Time {
	if timeRange.EndTime == nil {
		return nil
	}
	if timeRange.EndTime.IsZero() {
		return nil
	}
	return timeRange.EndTime
}

type UserSession struct {
	UserID       int64  `json:"userId"`
	Name         string `json:"name"`
	Token        string `json:"token"`
	PlatformType string `json:"platformType"`
	RoleID       int64  `json:"roleId"`
}

func (userSession *UserSession) GetUserID() int64 {
	return userSession.UserID
}

func (userSession *UserSession) GetName() string {
	return userSession.Name
}

func (userSession *UserSession) GetToken() string {
	return userSession.Token
}

type OpResult struct {
	Success bool `json:"success"`
}

func OpSuccess() *OpResult {
	return &OpResult{
		Success: true,
	}
}

// 管理员操作列信息
type AdminOpInfo struct {
	Key  string `json:"key"`
	Name string `json:"name"`
	Show bool   `json:"show"`
}
