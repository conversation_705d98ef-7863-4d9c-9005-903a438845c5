// package api

// import (
// 	"errors"
// 	"study_platform/common"

// 	"github.com/gofiber/fiber/v2"
// )

// func PostApiNoAuth[Req any, Rsp any](app fiber.Router, path string, handler func(c *fiber.Ctx, req *Req) (*Rsp, error)) {
// 	app.Post(path, func(c *fiber.Ctx) error {
// 		req, err := common.GetBodyRequestNoAuth[Req](c)
// 		if err != nil {
// 			return common.BadRequestRsp(c, err)
// 		}
// 		rsp, err := handler(c, req)
// 		if err != nil {
// 			return common.ServerErrorRsp(c, err)
// 		}
// 		return common.SuccessWithDataRsp(c, rsp)
// 	})
// }

// func PostApi[Req any, Rsp any](app fiber.Router, path string, handler func(c *fiber.Ctx, session *common.UserSession, req *Req) (*Rsp, error)) {
// 	sessionProvider := cache.GetUserSessionCache()
// 	app.Post(path, func(c *fiber.Ctx) error {
// 		req, session, err := common.GetBodyRequest[Req](c, sessionProvider)
// 		if err != nil {
// 			if req == nil {
// 				return common.BadRequestRsp(c, err)
// 			} else if session == nil {
// 				return common.UnauthorizedRsp(c, errors.New("请先登录"))
// 			} else {
// 				return common.ServerErrorRsp(c, err)
// 			}
// 		}

// 		rsp, err := handler(c, session, req)
// 		if err != nil {
// 			return common.ServerErrorRsp(c, err)
// 		}
// 		return common.SuccessWithDataRsp(c, rsp)
// 	})
// }

// func UseUserAPI(app fiber.Router) {
// 	// 登录
// 	// PostApiNoAuth(app, "/login/codeLogin", loginAPI.codeLogin)
// 	// PostApiNoAuth(app, "/login/passwordLogin", loginAPI.passwordLogin)
// 	// PostApiNoAuth(app, "/login/resetPassword", loginAPI.resetPassword)
// 	// PostApi(app, "/login/logout", loginAPI.logout)

// 	// // 系统接口
// 	// PostApiNoAuth(app, "/sys/sendSMSCode", sysAPI.sendSMSCode)
// 	// PostApi(app, "/sys/getUploadPolicyToken", sysAPI.getUploadPolicyToken)
// 	// PostApi(app, "/sys/getAreaList", sysAPI.getAreaList)
// 	// PostApi(app, "/sys/searchBank", sysAPI.searchBank)
// 	// PostApi(app, "/sys/getAppInfo", sysAPI.getAppInfo)

// 	// // 用户信息
// 	// PostApi(app, "/user/changePassword", userAPI.changePassword)
// 	// PostApi(app, "/user/getUserInfo", userAPI.getUserInfo)
// 	// PostApi(app, "/user/getBankCardList", userAPI.getBankCardList)
// 	// PostApi(app, "/user/getUserAddressList", userAPI.getUserAddressList)
// 	// PostApi(app, "/user/addUserAddress", userAPI.addUserAddress)
// 	// PostApi(app, "/user/updateUserAddress", userAPI.updateUserAddress)
// 	// PostApi(app, "/user/deleteUserAddress", userAPI.deleteUserAddress)
// 	// PostApi(app, "/user/setDefaultUserAddress", userAPI.setDefaultUserAddress)

// 	// // 余额
// 	// PostApi(app, "/balance/getBalance", balanceAPI.getBalance)
// 	// PostApi(app, "/balance/getDepositInfo", balanceAPI.getDepositInfo)
// 	// PostApi(app, "/balance/getBalanceRecordPage", balanceAPI.getBalanceRecordPage)
// 	// PostApi(app, "/balance/getBalanceLogPage", balanceAPI.getBalanceLogPage)
// 	// PostApi(app, "/balance/withdrawApply", balanceAPI.withdrawApply)

// 	// // 交易
// 	// PostApi(app, "/metal/searchSeller", tradeMetalAPI.searchSeller)
// 	// PostApi(app, "/metal/getUnsettledOrderPage", tradeMetalAPI.getUnsettledOrderPage)
// 	// PostApi(app, "/metal/getSettledOrderPage", tradeMetalAPI.getSettledOrderPage)
// 	// PostApi(app, "/metal/getOrderDetail", tradeMetalAPI.getOrderDetail)
// 	// PostApi(app, "/metal/putOrder", tradeMetalAPI.putOrder)
// 	// PostApi(app, "/metal/confirmPay", tradeMetalAPI.confirmPay)
// 	// PostApi(app, "/metal/cancelOrder", tradeMetalAPI.cancelOrder)

// }
