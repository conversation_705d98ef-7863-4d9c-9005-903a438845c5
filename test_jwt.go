package main

import (
	"fmt"
	"time"
	"study_platform/util"
)

func main() {
	fmt.Println("=== JWT工具类测试 ===")
	
	// 测试生成令牌
	fmt.Println("\n1. 生成JWT令牌")
	userID := uint(123)
	username := "testuser"
	email := "<EMAIL>"
	role := "user"
	
	token, err := util.JWT.GenerateToken(userID, username, email, role)
	if err != nil {
		fmt.Printf("生成令牌失败: %v\n", err)
		return
	}
	fmt.Printf("生成的令牌: %s\n", token)
	
	// 测试解析令牌
	fmt.Println("\n2. 解析JWT令牌")
	claims, err := util.JWT.ParseToken(token)
	if err != nil {
		fmt.Printf("解析令牌失败: %v\n", err)
		return
	}
	fmt.Printf("用户ID: %d\n", claims.UserID)
	fmt.Printf("用户名: %s\n", claims.Username)
	fmt.Printf("邮箱: %s\n", claims.Email)
	fmt.Printf("角色: %s\n", claims.Role)
	fmt.Printf("发行者: %s\n", claims.Issuer)
	fmt.Printf("过期时间: %s\n", claims.ExpiresAt.Time.Format("2006-01-02 15:04:05"))
	
	// 测试验证令牌
	fmt.Println("\n3. 验证JWT令牌")
	isValid := util.JWT.ValidateToken(token)
	fmt.Printf("令牌是否有效: %t\n", isValid)
	
	// 测试从令牌获取信息
	fmt.Println("\n4. 从令牌获取信息")
	
	extractedUserID, err := util.JWT.GetUserIDFromToken(token)
	if err != nil {
		fmt.Printf("获取用户ID失败: %v\n", err)
	} else {
		fmt.Printf("提取的用户ID: %d\n", extractedUserID)
	}
	
	extractedUsername, err := util.JWT.GetUsernameFromToken(token)
	if err != nil {
		fmt.Printf("获取用户名失败: %v\n", err)
	} else {
		fmt.Printf("提取的用户名: %s\n", extractedUsername)
	}
	
	extractedRole, err := util.JWT.GetRoleFromToken(token)
	if err != nil {
		fmt.Printf("获取角色失败: %v\n", err)
	} else {
		fmt.Printf("提取的角色: %s\n", extractedRole)
	}
	
	// 测试令牌过期检查
	fmt.Println("\n5. 检查令牌过期状态")
	isExpired := util.JWT.IsTokenExpired(token)
	fmt.Printf("令牌是否已过期: %t\n", isExpired)
	
	expireTime, err := util.JWT.GetTokenExpireTime(token)
	if err != nil {
		fmt.Printf("获取过期时间失败: %v\n", err)
	} else {
		fmt.Printf("令牌过期时间: %s\n", expireTime.Format("2006-01-02 15:04:05"))
		fmt.Printf("距离过期还有: %s\n", time.Until(expireTime).String())
	}
	
	// 测试无效令牌
	fmt.Println("\n6. 测试无效令牌")
	invalidToken := "invalid.token.here"
	isValidInvalid := util.JWT.ValidateToken(invalidToken)
	fmt.Printf("无效令牌验证结果: %t\n", isValidInvalid)
	
	// 测试创建新的JWT工具实例
	fmt.Println("\n7. 测试自定义JWT工具")
	customJWT := util.NewJWTUtil("custom-secret", "custom-issuer", 1*time.Hour)
	customToken, err := customJWT.GenerateToken(456, "customuser", "<EMAIL>", "admin")
	if err != nil {
		fmt.Printf("生成自定义令牌失败: %v\n", err)
	} else {
		fmt.Printf("自定义令牌: %s\n", customToken)
		
		customClaims, err := customJWT.ParseToken(customToken)
		if err != nil {
			fmt.Printf("解析自定义令牌失败: %v\n", err)
		} else {
			fmt.Printf("自定义令牌用户ID: %d\n", customClaims.UserID)
			fmt.Printf("自定义令牌发行者: %s\n", customClaims.Issuer)
		}
	}
	
	fmt.Println("\n=== JWT测试完成 ===")
}
