package main

import (
	"fmt"
	"study_platform/util"
)

// 演示JWT工具类的基本用法
func main() {
	fmt.Println("=== JWT工具类使用示例 ===")
	
	// 1. 用户登录时生成令牌
	fmt.Println("\n1. 用户登录 - 生成JWT令牌")
	userID := uint(1001)
	username := "john_doe"
	email := "<EMAIL>"
	role := "admin"
	
	token, err := util.JWT.GenerateToken(userID, username, email, role)
	if err != nil {
		fmt.Printf("生成令牌失败: %v\n", err)
		return
	}
	fmt.Printf("登录成功，令牌: %s\n", token)
	
	// 2. 验证令牌（中间件场景）
	fmt.Println("\n2. 验证用户令牌")
	if util.JWT.ValidateToken(token) {
		fmt.Println("✅ 令牌验证通过")
		
		// 3. 从令牌中提取用户信息
		fmt.Println("\n3. 提取用户信息")
		userID, _ := util.JWT.GetUserIDFromToken(token)
		username, _ := util.JWT.GetUsernameFromToken(token)
		role, _ := util.JWT.GetRoleFromToken(token)
		
		fmt.Printf("当前用户ID: %d\n", userID)
		fmt.Printf("当前用户名: %s\n", username)
		fmt.Printf("当前用户角色: %s\n", role)
		
		// 4. 权限检查示例
		fmt.Println("\n4. 权限检查")
		if role == "admin" {
			fmt.Println("✅ 管理员权限验证通过")
		} else {
			fmt.Println("❌ 权限不足")
		}
	} else {
		fmt.Println("❌ 令牌验证失败")
	}
	
	// 5. 令牌刷新示例（需要修改过期时间来测试）
	fmt.Println("\n5. 令牌状态检查")
	if util.JWT.IsTokenExpired(token) {
		fmt.Println("令牌已过期，需要重新登录")
	} else {
		expireTime, _ := util.JWT.GetTokenExpireTime(token)
		fmt.Printf("令牌有效，过期时间: %s\n", expireTime.Format("2006-01-02 15:04:05"))
	}
}
