package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// RedisCache Redis缓存客户端
type RedisCache struct {
	client *redis.Client
	ctx    context.Context
}

// NewRedisCache 创建新的Redis缓存实例
func NewRedisCache(addr, password string, db int) *RedisCache {
	rdb := redis.NewClient(&redis.Options{
		Addr:     addr,     // Redis地址，例如 "localhost:6379"
		Password: password, // Redis密码，没有密码则为空字符串
		DB:       db,       // Redis数据库索引
	})

	return &RedisCache{
		client: rdb,
		ctx:    context.Background(),
	}
}

// Set 设置键值对，可选过期时间
func (r *RedisCache) Set(key string, value interface{}, expiration time.Duration) error {
	// 将值序列化为JSON
	jsonValue, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("序列化值失败: %w", err)
	}

	// 设置到Redis
	err = r.client.Set(r.ctx, key, jsonValue, expiration).Err()
	if err != nil {
		return fmt.Errorf("设置Redis键值失败: %w", err)
	}

	return nil
}

// Get 获取值并反序列化到指定类型
func (r *RedisCache) Get(key string, dest interface{}) error {
	// 从Redis获取值
	val, err := r.client.Get(r.ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return fmt.Errorf("键 '%s' 不存在", key)
		}
		return fmt.Errorf("获取Redis值失败: %w", err)
	}

	// 反序列化JSON到目标对象
	err = json.Unmarshal([]byte(val), dest)
	if err != nil {
		return fmt.Errorf("反序列化值失败: %w", err)
	}

	return nil
}

// GetString 获取字符串值
func (r *RedisCache) GetString(key string) (string, error) {
	val, err := r.client.Get(r.ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return "", fmt.Errorf("键 '%s' 不存在", key)
		}
		return "", fmt.Errorf("获取Redis值失败: %w", err)
	}
	return val, nil
}

// SetString 设置字符串值
func (r *RedisCache) SetString(key, value string, expiration time.Duration) error {
	err := r.client.Set(r.ctx, key, value, expiration).Err()
	if err != nil {
		return fmt.Errorf("设置Redis字符串失败: %w", err)
	}
	return nil
}

// Delete 删除键
func (r *RedisCache) Delete(key string) error {
	err := r.client.Del(r.ctx, key).Err()
	if err != nil {
		return fmt.Errorf("删除Redis键失败: %w", err)
	}
	return nil
}

// Exists 检查键是否存在
func (r *RedisCache) Exists(key string) (bool, error) {
	count, err := r.client.Exists(r.ctx, key).Result()
	if err != nil {
		return false, fmt.Errorf("检查Redis键存在性失败: %w", err)
	}
	return count > 0, nil
}

// SetExpire 为已存在的键设置过期时间
func (r *RedisCache) SetExpire(key string, expiration time.Duration) error {
	err := r.client.Expire(r.ctx, key, expiration).Err()
	if err != nil {
		return fmt.Errorf("设置Redis键过期时间失败: %w", err)
	}
	return nil
}

// GetTTL 获取键的剩余生存时间
func (r *RedisCache) GetTTL(key string) (time.Duration, error) {
	ttl, err := r.client.TTL(r.ctx, key).Result()
	if err != nil {
		return 0, fmt.Errorf("获取Redis键TTL失败: %w", err)
	}
	return ttl, nil
}

// Ping 测试Redis连接
func (r *RedisCache) Ping() error {
	_, err := r.client.Ping(r.ctx).Result()
	if err != nil {
		return fmt.Errorf("Redis连接测试失败: %w", err)
	}
	return nil
}

// Close 关闭Redis连接
func (r *RedisCache) Close() error {
	return r.client.Close()
}

// GetKeys 获取匹配模式的所有键
func (r *RedisCache) GetKeys(pattern string) ([]string, error) {
	keys, err := r.client.Keys(r.ctx, pattern).Result()
	if err != nil {
		return nil, fmt.Errorf("获取Redis键列表失败: %w", err)
	}
	return keys, nil
}

// Increment 原子性递增
func (r *RedisCache) Increment(key string) (int64, error) {
	val, err := r.client.Incr(r.ctx, key).Result()
	if err != nil {
		return 0, fmt.Errorf("Redis递增操作失败: %w", err)
	}
	return val, nil
}

// IncrementBy 原子性递增指定值
func (r *RedisCache) IncrementBy(key string, value int64) (int64, error) {
	val, err := r.client.IncrBy(r.ctx, key, value).Result()
	if err != nil {
		return 0, fmt.Errorf("Redis递增操作失败: %w", err)
	}
	return val, nil
}

// Decrement 原子性递减
func (r *RedisCache) Decrement(key string) (int64, error) {
	val, err := r.client.Decr(r.ctx, key).Result()
	if err != nil {
		return 0, fmt.Errorf("Redis递减操作失败: %w", err)
	}
	return val, nil
}
