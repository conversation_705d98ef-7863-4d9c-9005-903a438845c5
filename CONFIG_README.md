# 配置系统使用指南

## 概述

这个配置系统提供了完整的应用程序配置管理，包括：
- 应用程序基础配置
- Redis 配置
- MySQL 配置
- 环境变量支持
- 数据库连接管理

## 文件结构

```
config/
├── config.go      # 主配置结构体和加载逻辑
├── init.go        # 数据库初始化和管理
├── example.go     # 使用示例
└── README.md      # 说明文档

.env.example       # 环境变量示例文件
main_with_config.go # 完整的使用示例
```

## 快速开始

### 1. 基本使用

```go
package main

import (
    "study_platform/config"
)

func main() {
    // 加载配置
    cfg := config.LoadConfig()
    
    // 使用配置
    fmt.Printf("应用名称: %s\n", cfg.App.Name)
    fmt.Printf("Redis地址: %s\n", cfg.Redis.GetAddr())
    fmt.Printf("MySQL DSN: %s\n", cfg.MySQL.GetDSN())
}
```

### 2. 初始化数据库

```go
// 初始化数据库连接
dbManager, err := config.InitDatabases(cfg)
if err != nil {
    log.Fatalf("数据库初始化失败: %v", err)
}
defer dbManager.Close()

// 使用MySQL
db := dbManager.MySQL

// 使用Redis
rdb := dbManager.Redis
```

### 3. 环境变量配置

复制 `.env.example` 为 `.env` 并修改配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
# 应用程序配置
APP_NAME=Learn3D
APP_ENV=development
APP_PORT=3000

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# MySQL 配置
MYSQL_HOST=localhost
MYSQL_USERNAME=root
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=learn3d
```

## 配置结构

### AppConfig - 应用配置

| 字段 | 类型 | 环境变量 | 默认值 | 说明 |
|------|------|----------|--------|------|
| Name | string | APP_NAME | Learn3D | 应用名称 |
| Version | string | APP_VERSION | 1.0.0 | 应用版本 |
| Environment | string | APP_ENV | development | 运行环境 |
| Port | string | APP_PORT | 3000 | 服务端口 |
| Host | string | APP_HOST | localhost | 服务主机 |
| Debug | bool | APP_DEBUG | true | 调试模式 |
| LogLevel | string | LOG_LEVEL | info | 日志级别 |

### RedisConfig - Redis配置

| 字段 | 类型 | 环境变量 | 默认值 | 说明 |
|------|------|----------|--------|------|
| Host | string | REDIS_HOST | localhost | Redis主机 |
| Port | string | REDIS_PORT | 6379 | Redis端口 |
| Password | string | REDIS_PASSWORD | "" | Redis密码 |
| Database | int | REDIS_DB | 0 | 数据库索引 |
| MaxRetries | int | REDIS_MAX_RETRIES | 3 | 最大重试次数 |
| PoolSize | int | REDIS_POOL_SIZE | 10 | 连接池大小 |

### MySQLConfig - MySQL配置

| 字段 | 类型 | 环境变量 | 默认值 | 说明 |
|------|------|----------|--------|------|
| Host | string | MYSQL_HOST | localhost | MySQL主机 |
| Port | string | MYSQL_PORT | 3306 | MySQL端口 |
| Username | string | MYSQL_USERNAME | root | 用户名 |
| Password | string | MYSQL_PASSWORD | "" | 密码 |
| Database | string | MYSQL_DATABASE | learn3d | 数据库名 |
| Charset | string | MYSQL_CHARSET | utf8mb4 | 字符集 |
| MaxIdleConns | int | MYSQL_MAX_IDLE_CONNS | 10 | 最大空闲连接 |
| MaxOpenConns | int | MYSQL_MAX_OPEN_CONNS | 100 | 最大打开连接 |

## 高级功能

### 1. 环境判断

```go
cfg := config.GetConfig()

if cfg.IsDevelopment() {
    // 开发环境逻辑
}

if cfg.IsProduction() {
    // 生产环境逻辑
}

if cfg.IsTest() {
    // 测试环境逻辑
}
```

### 2. 配置验证

```go
if err := config.ValidateConfig(cfg); err != nil {
    log.Fatalf("配置验证失败: %v", err)
}
```

### 3. 打印配置信息

```go
config.PrintConfig(cfg) // 仅在调试模式下打印
```

### 4. 全局配置访问

```go
// 在任何地方获取全局配置
cfg := config.GetConfig()
```

## 运行示例

### 1. 运行配置示例

```bash
go run config/example.go
```

### 2. 运行完整应用示例

```bash
go run main_with_config.go
```

### 3. 测试健康检查

```bash
curl http://localhost:3000/health
```

### 4. 查看配置信息（开发环境）

```bash
curl http://localhost:3000/config
```

### 5. 测试缓存功能

```bash
curl http://localhost:3000/cache/test
```

## 注意事项

1. **环境变量优先级**: 环境变量 > 默认值
2. **敏感信息**: 不要在代码中硬编码密码等敏感信息
3. **生产环境**: 生产环境建议设置 `APP_DEBUG=false`
4. **连接池**: 根据实际负载调整数据库连接池参数
5. **日志级别**: 生产环境建议使用 `error` 或 `warn` 级别

## 扩展配置

如需添加新的配置项，请按以下步骤：

1. 在相应的配置结构体中添加字段
2. 在加载函数中添加环境变量读取逻辑
3. 在 `.env.example` 中添加示例配置
4. 更新文档说明

## 故障排除

### 常见问题

1. **Redis连接失败**: 检查Redis服务是否启动，端口是否正确
2. **MySQL连接失败**: 检查MySQL服务、用户名密码、数据库是否存在
3. **配置加载失败**: 检查环境变量格式是否正确
4. **端口占用**: 更改 `APP_PORT` 环境变量

### 调试技巧

1. 设置 `APP_DEBUG=true` 查看详细日志
2. 使用 `/health` 端点检查服务状态
3. 使用 `/config` 端点查看当前配置（仅开发环境）
