package common

import (
	"net/http"

	// 使用不同的别名

	"github.com/gofiber/fiber/v2"
)

// 定义一个泛型结构体
type Response[T any] struct {
	Data *T     `json:"data"`
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

func (response *Response[T]) GetData() *T {
	return response.Data
}

func (response *Response[T]) GetCode() int {
	return response.Code
}

func (response *Response[T]) GetMsg() string {
	return response.Msg
}

func _Success[T any](data *T) *Response[T] {
	return &Response[T]{
		Data: data,
		Code: 0,
		Msg:  "success",
	}
}

func _Error[T any](msg string) *Response[T] {
	return &Response[T]{
		Data: nil,
		Code: 1,
		Msg:  msg,
	}
}

type _ResultRsp struct {
	Success bool `json:"success"`
}

func (result *_ResultRsp) IsSuccess() bool {
	return result.Success
}

func _SuccessResultRsp() *_ResultRsp {
	return &_ResultRsp{
		Success: true,
	}
}

func _ErrorResultRsp() *_ResultRsp {
	return &_ResultRsp{
		Success: false,
	}
}

func SuccessRsp(c *fiber.Ctx) error {
	data := _SuccessResultRsp()
	return c.JSON(_Success(data))
}

func SuccessWithDataRsp[T any](c *fiber.Ctx, data *T) error {
	return c.JSON(_Success(data))
}

func ErrorRsp(c *fiber.Ctx, err error) error {
	return c.JSON(_Error[any](err.Error()))
}

func BadRequestRsp(c *fiber.Ctx, err error) error {
	return c.Status(http.StatusBadRequest).JSON(_Error[any](err.Error()))
}

func UnauthorizedRsp(c *fiber.Ctx, err error) error {
	return c.Status(http.StatusUnauthorized).JSON(_Error[any](err.Error()))
}

func ForbiddenRsp(c *fiber.Ctx, err error) error {
	return c.Status(http.StatusForbidden).JSON(_Error[any](err.Error()))
}

func NotFoundRsp(c *fiber.Ctx, err error) error {
	return c.Status(http.StatusNotFound).JSON(_Error[any](err.Error()))
}

func ServerErrorRsp(c *fiber.Ctx, err error) error {
	return c.Status(http.StatusInternalServerError).JSON(_Error[any](err.Error()))
}
